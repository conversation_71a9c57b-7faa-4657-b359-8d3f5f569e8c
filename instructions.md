Take a look at @ti-platform/aide and @ti-platform/client-airtable to see how the code is structured as well as understanding the code style.
You will be creating a new package, @ti-platform/di which will handle dependency injection. Take a look at how I would be using the package below and then create the code to support it:

```ts
// beans/logger.ts
import { defineBean } from '@ti-platform/di';

export const logger = defineBean(() => {
    return {
        log: (message: string) => {
            console.log(message);
        },
    };
});
```

```ts
// beans/database.ts
import { defineBean } from '@ti-platform/di';
import { logger } from './logger';

export const database = defineBean((get) => {
    const { log } = get(logger);
    return {
        connect: () => {
            log('Connecting to database');
        },
    };
});
```

```ts
// beans/server.ts
import { defineBean } from '@ti-platform/di';
import { logger } from './logger';
import { database } from './database';

export const server = defineBean((get) => {
    const { log } = get(logger);
    const { connect } = get(database);
    return {
        start: () => {
            log('Starting server');
            connect();
        },
    };
});
```

```ts
// index.ts
import { createContainer } from '@ti-platform/di';
import { server } from './beans/server';

const container = createContainer();

const { start } = container.resolve(server);
start();
```

There are a few things to note:

1. The `defineBean` function is used to define a bean. It takes a factory function that returns the bean. The factory function takes a `get` function as its argument. The `get` function is used to retrieve other beans.
2. The `createContainer` function is used to create a container. The container is used to resolve beans.
3. The `container.resolve` function is used to resolve a bean. It takes a bean definition and returns the bean.
4. Beans are created only once. Subsequent calls to `container.resolve` will return the same instance.
5. The `get` function will throw an error if the bean is not found or cannot be created.
6. The `createContainer` function takes an optional configuration object. The configuration object can be used to provide initial values for beans.
7. The `defineBean`, `get`, and `resolve` functions are aware of types. So defining a bean and having it return type A means that when calling get or resolve, the return item will be of type A.
8. The `get` function can be used to retrieve beans that have not been created yet. The container will automatically create the bean when it is retrieved.
9. The `createContainer` function can take a configuration object that allows you to provide initial values for beans. If a bean is requested that has an initial value, the factory function for that bean will not be called.
10. As beans are being created, if it detects a circular dependency, it will throw an error.
