# @ti-platform/di

A lightweight, type-safe dependency injection container for TypeScript applications. This package provides a simple yet powerful DI system with singleton behavior, lazy instantiation, circular dependency detection, and full TypeScript support.

## Features

* **Type Safety**: Full TypeScript support with proper type inference
* **Singleton Behavior**: Each bean is instantiated only once per container
* **Lazy Instantiation**: Beans are created only when first requested
* **Dependency Injection**: Factory functions receive a `get()` function to retrieve dependencies
* **Circular Dependency Detection**: Clear error messages when circular dependencies are detected
* **Configuration Override**: Allow providing initial values that bypass factory functions
* **Zero Dependencies**: Lightweight with no external dependencies

## Installation

```bash
pnpm add @ti-platform/di
```

## Quick Start

```typescript
import { defineBean, createContainer } from '@ti-platform/di';

// Define your beans
const configBean = defineBean(() => ({
  apiUrl: 'https://api.example.com',
  timeout: 5000
}));

const apiServiceBean = defineBean((get) => {
  const config = get(configBean);
  return new ApiService(config.apiUrl, config.timeout);
});

// Create a container and resolve dependencies
const container = createContainer();
const apiService = container.resolve(apiServiceBean);
```

## Classes

### BeanNotFoundError

Defined in: packages/di/src/errors.ts:55

Error thrown when trying to resolve a bean that doesn't exist or is undefined.

#### Extends

* [`DIError`](#dierror)

#### Constructors

##### Constructor

> **new BeanNotFoundError**(`bean`): [`BeanNotFoundError`](#beannotfounderror)

Defined in: packages/di/src/errors.ts:56

###### Parameters

| Parameter | Type                                           |
| --------- | ---------------------------------------------- |
| `bean`    | [`BeanDefinition`](#beandefinition)<`unknown`> |

###### Returns

[`BeanNotFoundError`](#beannotfounderror)

###### Overrides

[`DIError`](#dierror).[`constructor`](#constructor-3)

#### Properties

| Property                                            | Modifier   | Type                                           | Description                                                                                                          | Inherited from                                                    |
| --------------------------------------------------- | ---------- | ---------------------------------------------- | -------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------- |
| <a id="bean"></a> `bean`                            | `readonly` | [`BeanDefinition`](#beandefinition)<`unknown`> | -                                                                                                                    | -                                                                 |
| <a id="cause"></a> `cause?`                         | `readonly` | `Error`                                        | -                                                                                                                    | [`DIError`](#dierror).[`cause`](#cause-3)                         |
| <a id="message"></a> `message`                      | `public`   | `string`                                       | -                                                                                                                    | [`DIError`](#dierror).[`message`](#message-3)                     |
| <a id="name"></a> `name`                            | `public`   | `string`                                       | -                                                                                                                    | [`DIError`](#dierror).[`name`](#name-3)                           |
| <a id="stack"></a> `stack?`                         | `public`   | `string`                                       | -                                                                                                                    | [`DIError`](#dierror).[`stack`](#stack-3)                         |
| <a id="preparestacktrace"></a> `prepareStackTrace?` | `static`   | (`err`, `stackTraces`) => `any`                | Optional override for formatting stack traces **See** <https://v8.dev/docs/stack-trace-api#customizing-stack-traces> | [`DIError`](#dierror).[`prepareStackTrace`](#preparestacktrace-3) |
| <a id="stacktracelimit"></a> `stackTraceLimit`      | `static`   | `number`                                       | -                                                                                                                    | [`DIError`](#dierror).[`stackTraceLimit`](#stacktracelimit-3)     |

#### Methods

##### captureStackTrace()

> `static` **captureStackTrace**(`targetObject`, `constructorOpt`?): `void`

Defined in: common/temp/node\_modules/.pnpm/@types+node\@22.14.0/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

###### Parameters

| Parameter         | Type       |
| ----------------- | ---------- |
| `targetObject`    | `object`   |
| `constructorOpt`? | `Function` |

###### Returns

`void`

###### Inherited from

[`DIError`](#dierror).[`captureStackTrace`](#capturestacktrace-6)

***

### BeanResolutionError

Defined in: packages/di/src/errors.ts:42

Error thrown when a bean cannot be resolved due to factory function failure.

#### Extends

* [`DIError`](#dierror)

#### Constructors

##### Constructor

> **new BeanResolutionError**(`bean`, `cause`): [`BeanResolutionError`](#beanresolutionerror)

Defined in: packages/di/src/errors.ts:43

###### Parameters

| Parameter | Type                                           |
| --------- | ---------------------------------------------- |
| `bean`    | [`BeanDefinition`](#beandefinition)<`unknown`> |
| `cause`   | `Error`                                        |

###### Returns

[`BeanResolutionError`](#beanresolutionerror)

###### Overrides

[`DIError`](#dierror).[`constructor`](#constructor-3)

#### Properties

| Property                                              | Modifier   | Type                                           | Description                                                                                                          | Inherited from                                                    |
| ----------------------------------------------------- | ---------- | ---------------------------------------------- | -------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------- |
| <a id="bean-1"></a> `bean`                            | `readonly` | [`BeanDefinition`](#beandefinition)<`unknown`> | -                                                                                                                    | -                                                                 |
| <a id="cause-1"></a> `cause?`                         | `readonly` | `Error`                                        | -                                                                                                                    | [`DIError`](#dierror).[`cause`](#cause-3)                         |
| <a id="message-1"></a> `message`                      | `public`   | `string`                                       | -                                                                                                                    | [`DIError`](#dierror).[`message`](#message-3)                     |
| <a id="name-1"></a> `name`                            | `public`   | `string`                                       | -                                                                                                                    | [`DIError`](#dierror).[`name`](#name-3)                           |
| <a id="stack-1"></a> `stack?`                         | `public`   | `string`                                       | -                                                                                                                    | [`DIError`](#dierror).[`stack`](#stack-3)                         |
| <a id="preparestacktrace-1"></a> `prepareStackTrace?` | `static`   | (`err`, `stackTraces`) => `any`                | Optional override for formatting stack traces **See** <https://v8.dev/docs/stack-trace-api#customizing-stack-traces> | [`DIError`](#dierror).[`prepareStackTrace`](#preparestacktrace-3) |
| <a id="stacktracelimit-1"></a> `stackTraceLimit`      | `static`   | `number`                                       | -                                                                                                                    | [`DIError`](#dierror).[`stackTraceLimit`](#stacktracelimit-3)     |

#### Methods

##### captureStackTrace()

> `static` **captureStackTrace**(`targetObject`, `constructorOpt`?): `void`

Defined in: common/temp/node\_modules/.pnpm/@types+node\@22.14.0/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

###### Parameters

| Parameter         | Type       |
| ----------------- | ---------- |
| `targetObject`    | `object`   |
| `constructorOpt`? | `Function` |

###### Returns

`void`

###### Inherited from

[`DIError`](#dierror).[`captureStackTrace`](#capturestacktrace-6)

***

### CircularDependencyError

Defined in: packages/di/src/errors.ts:24

Error thrown when a circular dependency is detected during bean resolution.

#### Extends

* [`DIError`](#dierror)

#### Constructors

##### Constructor

> **new CircularDependencyError**(`dependencyChain`, `circularBean`): [`CircularDependencyError`](#circulardependencyerror)

Defined in: packages/di/src/errors.ts:25

###### Parameters

| Parameter         | Type                                              |
| ----------------- | ------------------------------------------------- |
| `dependencyChain` | [`BeanDefinition`](#beandefinition)<`unknown`>\[] |
| `circularBean`    | [`BeanDefinition`](#beandefinition)<`unknown`>    |

###### Returns

[`CircularDependencyError`](#circulardependencyerror)

###### Overrides

[`DIError`](#dierror).[`constructor`](#constructor-3)

#### Properties

| Property                                              | Modifier   | Type                                              | Description                                                                                                          | Inherited from                                                    |
| ----------------------------------------------------- | ---------- | ------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------- |
| <a id="cause-2"></a> `cause?`                         | `readonly` | `Error`                                           | -                                                                                                                    | [`DIError`](#dierror).[`cause`](#cause-3)                         |
| <a id="circularbean"></a> `circularBean`              | `readonly` | [`BeanDefinition`](#beandefinition)<`unknown`>    | -                                                                                                                    | -                                                                 |
| <a id="dependencychain"></a> `dependencyChain`        | `readonly` | [`BeanDefinition`](#beandefinition)<`unknown`>\[] | -                                                                                                                    | -                                                                 |
| <a id="message-2"></a> `message`                      | `public`   | `string`                                          | -                                                                                                                    | [`DIError`](#dierror).[`message`](#message-3)                     |
| <a id="name-2"></a> `name`                            | `public`   | `string`                                          | -                                                                                                                    | [`DIError`](#dierror).[`name`](#name-3)                           |
| <a id="stack-2"></a> `stack?`                         | `public`   | `string`                                          | -                                                                                                                    | [`DIError`](#dierror).[`stack`](#stack-3)                         |
| <a id="preparestacktrace-2"></a> `prepareStackTrace?` | `static`   | (`err`, `stackTraces`) => `any`                   | Optional override for formatting stack traces **See** <https://v8.dev/docs/stack-trace-api#customizing-stack-traces> | [`DIError`](#dierror).[`prepareStackTrace`](#preparestacktrace-3) |
| <a id="stacktracelimit-2"></a> `stackTraceLimit`      | `static`   | `number`                                          | -                                                                                                                    | [`DIError`](#dierror).[`stackTraceLimit`](#stacktracelimit-3)     |

#### Methods

##### captureStackTrace()

> `static` **captureStackTrace**(`targetObject`, `constructorOpt`?): `void`

Defined in: common/temp/node\_modules/.pnpm/@types+node\@22.14.0/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

###### Parameters

| Parameter         | Type       |
| ----------------- | ---------- |
| `targetObject`    | `object`   |
| `constructorOpt`? | `Function` |

###### Returns

`void`

###### Inherited from

[`DIError`](#dierror).[`captureStackTrace`](#capturestacktrace-6)

***

### `abstract` DIError

Defined in: packages/di/src/errors.ts:6

Base error class for dependency injection related errors.

#### Extends

* `Error`

#### Extended by

* [`BeanNotFoundError`](#beannotfounderror)
* [`BeanResolutionError`](#beanresolutionerror)
* [`CircularDependencyError`](#circulardependencyerror)

#### Constructors

##### Constructor

> **new DIError**(`message`, `cause`?): [`DIError`](#dierror)

Defined in: packages/di/src/errors.ts:7

###### Parameters

| Parameter | Type     |
| --------- | -------- |
| `message` | `string` |
| `cause`?  | `Error`  |

###### Returns

[`DIError`](#dierror)

###### Overrides

`Error.constructor`

#### Properties

| Property                                              | Modifier   | Type                            | Description                                                                                                          | Inherited from            |
| ----------------------------------------------------- | ---------- | ------------------------------- | -------------------------------------------------------------------------------------------------------------------- | ------------------------- |
| <a id="cause-3"></a> `cause?`                         | `readonly` | `Error`                         | -                                                                                                                    | `Error.cause`             |
| <a id="message-3"></a> `message`                      | `public`   | `string`                        | -                                                                                                                    | `Error.message`           |
| <a id="name-3"></a> `name`                            | `public`   | `string`                        | -                                                                                                                    | `Error.name`              |
| <a id="stack-3"></a> `stack?`                         | `public`   | `string`                        | -                                                                                                                    | `Error.stack`             |
| <a id="preparestacktrace-3"></a> `prepareStackTrace?` | `static`   | (`err`, `stackTraces`) => `any` | Optional override for formatting stack traces **See** <https://v8.dev/docs/stack-trace-api#customizing-stack-traces> | `Error.prepareStackTrace` |
| <a id="stacktracelimit-3"></a> `stackTraceLimit`      | `static`   | `number`                        | -                                                                                                                    | `Error.stackTraceLimit`   |

#### Methods

##### captureStackTrace()

> `static` **captureStackTrace**(`targetObject`, `constructorOpt`?): `void`

Defined in: common/temp/node\_modules/.pnpm/@types+node\@22.14.0/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

###### Parameters

| Parameter         | Type       |
| ----------------- | ---------- |
| `targetObject`    | `object`   |
| `constructorOpt`? | `Function` |

###### Returns

`void`

###### Inherited from

`Error.captureStackTrace`

## Interfaces

### BeanDefinition\<T>

Defined in: packages/di/src/types.ts:29

Represents a bean definition that can be resolved by a container.
Contains the factory function and metadata for creating instances.

#### Type Parameters

| Type Parameter |
| -------------- |
| `T`            |

#### Properties

| Property                       | Modifier   | Type                                       | Description                                           |
| ------------------------------ | ---------- | ------------------------------------------ | ----------------------------------------------------- |
| <a id="factory"></a> `factory` | `readonly` | [`FactoryFunction`](#factoryfunction)<`T`> | Factory function that creates instances of this bean. |
| <a id="id"></a> `id`           | `readonly` | `symbol`                                   | Unique identifier for this bean definition.           |
| <a id="name-4"></a> `name?`    | `readonly` | `string`                                   | Optional name for debugging purposes.                 |

***

### Container

Defined in: packages/di/src/types.ts:50

Container interface for dependency injection.
Manages bean instances and their dependencies.

#### Methods

##### getInstantiatedBeans()

> **getInstantiatedBeans**(): `Map`<[`BeanDefinition`](#beandefinition)<`unknown`>, `unknown`>

Defined in: packages/di/src/types.ts:75

Gets all instantiated beans.

###### Returns

`Map`<[`BeanDefinition`](#beandefinition)<`unknown`>, `unknown`>

Map of bean definitions to their instances

##### isInstantiated()

> **isInstantiated**<`T`>(`bean`): `boolean`

Defined in: packages/di/src/types.ts:68

Checks if a bean has been instantiated.

###### Type Parameters

| Type Parameter |
| -------------- |
| `T`            |

###### Parameters

| Parameter | Type                                     | Description                  |
| --------- | ---------------------------------------- | ---------------------------- |
| `bean`    | [`BeanDefinition`](#beandefinition)<`T`> | The bean definition to check |

###### Returns

`boolean`

True if the bean has been instantiated

##### resolve()

> **resolve**<`T`>(`bean`): `T`

Defined in: packages/di/src/types.ts:60

Resolves a bean definition and returns its instance.
Ensures singleton behavior - same instance returned for subsequent calls.

###### Type Parameters

| Type Parameter |
| -------------- |
| `T`            |

###### Parameters

| Parameter | Type                                     | Description                    |
| --------- | ---------------------------------------- | ------------------------------ |
| `bean`    | [`BeanDefinition`](#beandefinition)<`T`> | The bean definition to resolve |

###### Returns

`T`

The resolved bean instance

###### Throws

If circular dependency is detected

###### Throws

If bean cannot be resolved

***

### ContainerConfig

Defined in: packages/di/src/types.ts:17

Configuration object for creating a container.
Allows providing initial values that bypass factory functions.

#### Properties

| Property                                    | Type                                                             | Description                                                                                             |
| ------------------------------------------- | ---------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------- |
| <a id="initialvalues"></a> `initialValues?` | `Map`<[`BeanDefinition`](#beandefinition)<`unknown`>, `unknown`> | Initial bean values that override factory functions. Key is the bean definition, value is the instance. |

## Type Aliases

### FactoryFunction()\<T>

> **FactoryFunction**<`T`> = (`get`) => `T`

Defined in: packages/di/src/types.ts:11

Factory function type that creates an instance of type T.
Can optionally use the get function to retrieve dependencies.

#### Type Parameters

| Type Parameter |
| -------------- |
| `T`            |

#### Parameters

| Parameter | Type                          |
| --------- | ----------------------------- |
| `get`     | [`GetFunction`](#getfunction) |

#### Returns

`T`

***

### GetFunction()

> **GetFunction** = <`T`>(`bean`) => `T`

Defined in: packages/di/src/types.ts:5

Function type for retrieving dependencies from the container.
Used within factory functions to access other beans.

#### Type Parameters

| Type Parameter |
| -------------- |
| `T`            |

#### Parameters

| Parameter | Type                                     |
| --------- | ---------------------------------------- |
| `bean`    | [`BeanDefinition`](#beandefinition)<`T`> |

#### Returns

`T`

## Functions

### createContainer()

> **createContainer**(`config`?): [`Container`](#container)

Defined in: packages/di/src/container.ts:91

Creates a new dependency injection container.

#### Parameters

| Parameter | Type                                  | Description                              |
| --------- | ------------------------------------- | ---------------------------------------- |
| `config`? | [`ContainerConfig`](#containerconfig) | Optional configuration for the container |

#### Returns

[`Container`](#container)

A new container instance

#### Example

```typescript
// Create a simple container
const container = createContainer();

// Create a container with initial values
const container = createContainer({
  initialValues: new Map([[configBean, { apiUrl: 'test://localhost' }]])
});
```

***

### defineBean()

> **defineBean**<`T`>(`factory`, `name`?): [`BeanDefinition`](#beandefinition)<`T`>

Defined in: packages/di/src/bean-definition.ts:37

Creates a bean definition with the given factory function.

#### Type Parameters

| Type Parameter |
| -------------- |
| `T`            |

#### Parameters

| Parameter | Type                                       | Description                               |
| --------- | ------------------------------------------ | ----------------------------------------- |
| `factory` | [`FactoryFunction`](#factoryfunction)<`T`> | Function that creates instances of type T |
| `name`?   | `string`                                   | Optional name for debugging purposes      |

#### Returns

[`BeanDefinition`](#beandefinition)<`T`>

A new bean definition

#### Example

```typescript
// Simple bean without dependencies
const configBean = defineBean(() => ({ apiUrl: 'https://api.example.com' }));

// Bean with dependencies
const serviceBean = defineBean((get) => {
  const config = get(configBean);
  return new ApiService(config.apiUrl);
});
```
