# @ti-platform/di

A lightweight, type-safe dependency injection container for TypeScript applications. This package provides a simple yet powerful DI system with singleton behavior, lazy instantiation, circular dependency detection, and full TypeScript support.

## Features

- **Type Safety**: Full TypeScript support with proper type inference
- **Singleton Behavior**: Each bean is instantiated only once per container
- **Lazy Instantiation**: Beans are created only when first requested
- **Dependency Injection**: Factory functions receive a `get()` function to retrieve dependencies
- **Circular Dependency Detection**: Clear error messages when circular dependencies are detected
- **Configuration Override**: Allow providing initial values that bypass factory functions
- **Zero Dependencies**: Lightweight with no external dependencies

## Installation

```bash
pnpm add @ti-platform/di
```

## Quick Start

```typescript
import { defineBean, createContainer } from '@ti-platform/di';

// Define your beans
const configBean = defineBean(() => ({
  apiUrl: 'https://api.example.com',
  timeout: 5000
}));

const apiServiceBean = defineBean((get) => {
  const config = get(configBean);
  return new ApiService(config.apiUrl, config.timeout);
});

// Create a container and resolve dependencies
const container = createContainer();
const apiService = container.resolve(apiServiceBean);
```

---Insert API Docs---
