
/**
 * Container interface for dependency injection.
 * Manages bean instances and their dependencies.
 */
export interface Container {
    /**
     * Resolves a bean definition and returns its instance.
     * Ensures singleton behavior - same instance returned for subsequent calls.
     *
     * @param bean The bean definition to resolve
     * @returns The resolved bean instance
     * @throws {CircularDependencyError} If circular dependency is detected
     * @throws {BeanResolutionError} If bean cannot be resolved
     */
    resolve<T>(bean: BeanDefinition<T>): T;

    /**
     * Checks if a bean has been instantiated.
     *
     * @param bean The bean definition to check
     * @returns True if the bean has been instantiated
     */
    isInstantiated<T>(bean: BeanDefinition<T>): boolean;

    /**
     * Gets all instantiated beans.
     *
     * @returns Map of bean definitions to their instances
     */
    getInstantiatedBeans(): Map<BeanDefinition<unknown>, unknown>;
}
