import type { BeanDefinition } from '@src/bean-definition';

/**
 * Base error class for dependency injection related errors.
 */
export abstract class DIError extends Error {
    constructor(
        message: string,
        public readonly cause?: Error
    ) {
        super(message);
        this.name = this.constructor.name;

        // Maintain proper stack trace for where our error was thrown (only available on V8)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}

/**
 * Error thrown when a circular dependency is detected during bean resolution.
 */
export class CircularDependencyError extends DIError {
    constructor(
        public readonly dependencyChain: Array<BeanDefinition<unknown>>,
        public readonly circularBean: BeanDefinition<unknown>
    ) {
        const chainNames = dependencyChain.map((bean) => bean.id.toString());
        const circularName = circularBean.id.toString();

        super(
            `Circular dependency detected: ${chainNames.join(' -> ')} -> ${circularName}. ` +
                `Bean '${circularName}' is already being resolved in the dependency chain.`
        );
    }
}

/**
 * Error thrown when a bean cannot be resolved due to factory function failure.
 */
export class BeanResolutionError extends DIError {
    constructor(
        public readonly bean: BeanDefinition<unknown>,
        cause: Error
    ) {
        const beanName = bean.id.toString();
        super(`Failed to resolve bean '${beanName}': ${cause.message}`, cause);
    }
}

/**
 * Error thrown when trying to resolve a bean that doesn't exist or is undefined.
 */
export class BeanNotFoundError extends DIError {
    constructor(public readonly bean: BeanDefinition<unknown>) {
        const beanName = bean?.id?.toString() ?? 'unknown';
        super(`Bean '${beanName}' not found or is undefined`);
    }
}
