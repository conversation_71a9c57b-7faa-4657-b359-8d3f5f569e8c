/**
 * Function type for retrieving dependencies from the container.
 * Used within factory functions to access other beans.
 */
export type GetFunction = <T>(bean: BeanDefinition<T>) => T;

/**
 * Factory function type that creates an instance of type T.
 * Can optionally use the get function to retrieve dependencies.
 */
export type FactoryFunction<T> = (get: GetFunction) => T;

/**
 * Represents a bean definition that can be resolved by a container.
 * Contains the factory function and metadata for creating instances.
 */
export type BeanDefinition<T> = {
    /**
     * Unique identifier for this bean definition.
     */
    readonly id: symbol;

    /**
     * Factory function that creates instances of this bean.
     */
    readonly factory: FactoryFunction<T>;
};

/**
 * Creates a bean definition with the given factory function.
 *
 * @param factory Function that creates instances of type T
 * @returns A new bean definition
 *
 * @example
 * ```typescript
 * // Simple bean without dependencies
 * const configBean = defineBean(() => ({ apiUrl: 'https://api.example.com' }));
 *
 * // Bean with dependencies
 * const serviceBean = defineBean((get) => {
 *   const config = get(configBean);
 *   return new ApiService(config.apiUrl);
 * });
 * ```
 */
export function defineBean<T>(factory: FactoryFunction<T>): BeanDefinition<T> {
    return { id: Symbol(), factory };
}
