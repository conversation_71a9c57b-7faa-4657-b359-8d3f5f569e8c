import type { BeanDefinition } from '@src/bean-definition';
import { BeanNotFoundError, BeanResolutionError, CircularDependencyError } from '@src/errors';
import { MapPlus } from '@ti-platform/aide';

/**
 * Manages bean instances and their dependencies.
 */
class Container {
    constructor(
        private readonly instances: MapPlus<symbol, unknown> = new MapPlus(),
        private readonly resolutionStack: Array<symbol> = []
    ) {}

    resolve<T>(bean: BeanDefinition<T>): T {
        // Return existing instance if already created (singleton behavior)
        if (this.instances.has(bean.id)) {
            return this.instances.get(bean.id) as T;
        }

        // Check for circular dependency
        if (this.resolutionStack.includes(bean.id)) {
            throw new CircularDependencyError([...this.resolutionStack], bean.id);
        }

        // Add to resolution stack for circular dependency detection
        this.resolutionStack.push(bean.id);

        try {
            // // Create get function for this resolution context
            // const get: GetFunction = <U>(dependency: BeanDefinition<U>): U => {
            //     return this.resolve(dependency);
            // };

            // // Create the instance using the factory function
            // const instance = bean.factory(get);

            // // Store the instance for singleton behavior
            // this.instances.set(bean, instance);

            // return instance;
            const instance = bean.factory(this.resolve);
            this.instances.set(bean.id, instance);
            return instance;
        } catch (error) {
            // If it's already a DI error, re-throw it
            if (error instanceof CircularDependencyError || error instanceof BeanNotFoundError) {
                throw error;
            }

            // Wrap other errors in BeanResolutionError
            throw new BeanResolutionError(bean, error as Error);
        } finally {
            // Remove from resolution stack
            this.resolutionStack.pop();
        }
    }

    // isInstantiated<T>(bean: BeanDefinition<T>): boolean {
    //     return this.instances.has(bean);
    // }

    // getInstantiatedBeans(): Map<BeanDefinition<unknown>, unknown> {
    //     return new Map(this.instances);
    // }
}

/**
 * Creates a new dependency injection container.
 */
export function createContainer(): Container {
    return new Container();
}
